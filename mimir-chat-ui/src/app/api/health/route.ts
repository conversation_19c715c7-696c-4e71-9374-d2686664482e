import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Basic health check
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      config: {
        hasBackendUrl: !!process.env.BACKEND_URL,
        hasOpenRouterKey: !!process.env.OPENROUTER_API_KEY,
        hasGithubToken: !!process.env.GITHUB_TOKEN,
        localDev: process.env.NEXT_PUBLIC_LOCAL_DEV === 'true'
      }
    };

    return NextResponse.json(health, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      { 
        status: 'unhealthy', 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }, 
      { status: 500 }
    );
  }
}

@import "tailwindcss";
@import "tw-animate-css";



@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.button-glass, .glass-btn, button, .btn {
  border-radius: 1.25rem !important;
  background: linear-gradient(135deg, rgba(173,216,230,0.55) 0%, rgba(255,255,255,0.35) 100%) !important;
  box-shadow: 0 8px 40px 0 rgba(80,120,255,0.13), 0 2px 12px 0 rgba(0,0,0,0.10) !important;
  border: 2px solid rgba(120,180,255,0.18) !important;
  backdrop-filter: blur(18px) saturate(1.5) !important;
  -webkit-backdrop-filter: blur(18px) saturate(1.5) !important;
  color: #2563eb !important;
  transition: box-shadow 0.2s, background 0.2s, border 0.2s;
}
.button-glass:hover, .glass-btn:hover, button:hover, .btn:hover {
  background: linear-gradient(135deg, rgba(173,216,230,0.75) 0%, rgba(255,255,255,0.55) 100%) !important;
  box-shadow: 0 12px 48px 0 rgba(80,120,255,0.18), 0 2px 12px 0 rgba(0,0,0,0.13) !important;
  border: 2.5px solid #a5b4fc !important;
}
@keyframes magic-flare-in {
  0% { opacity: 0; transform: scale(0.9) rotate(-6deg); filter: blur(8px) brightness(1.2); }
  60% { opacity: 1; transform: scale(1.05) rotate(2deg); filter: blur(2px) brightness(1.1); }
  100% { opacity: 1; transform: scale(1) rotate(0deg); filter: blur(0) brightness(1); }
}
@keyframes magic-flare-out {
  0% { opacity: 1; transform: scale(1) rotate(0deg); filter: blur(0) brightness(1); }
  60% { opacity: 0.7; transform: scale(1.08) rotate(-2deg); filter: blur(4px) brightness(1.1); }
  100% { opacity: 0; transform: scale(0.85) rotate(8deg); filter: blur(12px) brightness(1.3); }
}
.magic-flare-in {
  animation: magic-flare-in 0.8s cubic-bezier(.4,2,.6,1);
}
.magic-flare-out {
  animation: magic-flare-out 0.7s cubic-bezier(.4,2,.6,1) forwards;
}
@keyframes flare-in {
  0% { opacity: 0; transform: scale(0.95) translateY(20px); filter: blur(8px); }
  100% { opacity: 1; transform: scale(1) translateY(0); filter: blur(0); }
}
@keyframes flare-piece-out {
  0% { opacity: 1; filter: blur(0) brightness(1); transform: scale(1) translateY(0); }
  40% { opacity: 0.7; filter: blur(2px) brightness(1.1); transform: scale(1.05) translateY(-10px); }
  100% { opacity: 0; filter: blur(16px) brightness(1.3); transform: scale(1.15) translateY(-40px) rotate(8deg); }
}
.flare-in {
  animation: flare-in 0.7s cubic-bezier(.4,2,.6,1);
}
.flare-out {
  animation: none;
}
.welcome-flare-piece {
  transition: opacity 0.5s, filter 0.5s, transform 0.5s;
}
.flare-out .welcome-flare-piece {
  animation: flare-piece-out 2s cubic-bezier(.4,2,.6,1) forwards;
}
.flare-out .welcome-flare-piece:nth-child(2) {
  animation-delay: 0.2s;
}

.rpv-default-layout__toolbar {
  display: none !important;
}

.rpv-default-layout__main {
  height: 100% !important;
}

/* --- Liquid Glass Button Effect (from liquidglass.md, refined for all buttons) --- */

button, .btn-glass, a.btn-glass {
  position: relative;
  color: #1e293b;
  background: rgba(255, 255, 255, 0.18);
  border: 1.5px solid rgba(255, 255, 255, 0.55);
  border-radius: 0.75rem;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.10), 0 2px 8px 0 rgba(0,0,0,0.08);
  backdrop-filter: blur(6px) saturate(180%);
  -webkit-backdrop-filter: blur(6px) saturate(180%);
  padding: 0.38rem 0.85rem; /* smaller vertical/horizontal padding */
  font-weight: 600;
  font-family: inherit;
  font-size: 0.85rem; /* smaller font */
  cursor: pointer;
  transition: all 0.25s cubic-bezier(.4,2,.6,1);
  outline: none;
  text-decoration: none;
  z-index: 1;
  overflow: hidden;
  min-height: 2.1rem;
  min-width: 2.1rem;
  line-height: 1.1;
}

button svg, .btn-glass svg, a.btn-glass svg {
  width: 0.95em;
  height: 0.95em;
  vertical-align: middle;
  display: inline-block;
  fill: currentColor;
  stroke: currentColor;
  color: #2563eb;
  filter: none !important;
  /* Ensures icon is always visible and not blurred */
}

/* Specifically target arrow icons inside buttons and make them larger and more visible */
button svg.arrow, .btn-glass svg.arrow, a.btn-glass svg.arrow,
button svg.icon-arrow, .btn-glass svg.icon-arrow, a.btn-glass svg.icon-arrow {
  width: 1.35em;
  height: 1.35em;
  color: #1e293b;
  stroke-width: 2.2;
  filter: drop-shadow(0 0 2px #fff) drop-shadow(0 0 1.5px #2563eb);
}

button::after, .btn-glass::after, a.btn-glass::after {
  content: '';
  position: absolute;
  top: 0; left: 0; width: 100%; height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0.22) 0%, rgba(255,255,255,0.08) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 2;
  opacity: 0.7;
  transition: opacity 0.2s;
}

button:hover, .btn-glass:hover, a.btn-glass:hover {
  background: rgba(255,255,255,0.32);
  box-shadow: 0 12px 40px 0 rgba(80,120,255,0.18), 0 2px 12px 0 rgba(0,0,0,0.13);
  border-color: #a5b4fc;
  color: #2563eb;
  transform: scale(1.045);
}

button:active, .btn-glass:active, a.btn-glass:active {
  transform: scale(0.98);
  box-shadow: 0 4px 16px 0 rgba(80,120,255,0.10);
}

/* For white backgrounds, add a subtle border and shadow for contrast */
button, .btn-glass, a.btn-glass {
  border: 1.5px solid rgba(180, 200, 255, 0.22);
  box-shadow: 0 2px 8px 0 rgba(120,180,255,0.10), 0 1.5px 6px 0 rgba(0,0,0,0.06);
}

/* Remove default button background for consistency */
button {
  background: none;
}

/* Optional: add a glassy focus ring */
button:focus, .btn-glass:focus, a.btn-glass:focus {
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(96,165,250,0.18);
}

/* --- End Liquid Glass Button Effect --- */

.send-arrow {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(1);
  width: 1.2em !important;
  height: 1.2em !important;
  color: #2563eb !important;
  stroke: #2563eb !important;
  stroke-width: 2.2;
  filter: drop-shadow(0 0 2px #fff) drop-shadow(0 0 1.5px #2563eb);
  pointer-events: none;
}

.glass-hue-green {
  background: linear-gradient(90deg, rgba(120,255,180,0.32) 0%, rgba(255,255,255,0.45) 100%) !important;
  border: 1.5px solid rgba(120,255,180,0.22);
  box-shadow: 0 4px 24px 0 rgba(120,255,180,0.18), 0 1.5px 8px 0 rgba(0,0,0,0.08), 0 0 0 8px rgba(120,255,180,0.08) inset;
  backdrop-filter: blur(18px) saturate(2.2);
  -webkit-backdrop-filter: blur(18px) saturate(2.2);
  transition: background 0.5s cubic-bezier(.4,2,.6,1), box-shadow 0.5s cubic-bezier(.4,2,.6,1), border 0.5s cubic-bezier(.4,2,.6,1);
}
.glass-hue-red {
  background: linear-gradient(90deg, rgba(255,120,120,0.32) 0%, rgba(255,255,255,0.45) 100%) !important;
  border: 1.5px solid rgba(255,120,120,0.22);
  box-shadow: 0 4px 24px 0 rgba(255,120,120,0.18), 0 1.5px 8px 0 rgba(0,0,0,0.08), 0 0 0 8px rgba(255,120,120,0.08) inset;
  backdrop-filter: blur(18px) saturate(2.2);
  -webkit-backdrop-filter: blur(18px) saturate(2.2);
  transition: background 0.5s cubic-bezier(.4,2,.6,1), box-shadow 0.5s cubic-bezier(.4,2,.6,1), border 0.5s cubic-bezier(.4,2,.6,1);
}

.glass-input {
  background: linear-gradient(135deg, rgba(173,216,230,0.18) 0%, rgba(255,255,255,0.35) 100%) !important;
  border: 1.5px solid rgba(180, 200, 255, 0.22);
  box-shadow: 0 2px 8px 0 rgba(120,180,255,0.10), 0 1.5px 6px 0 rgba(0,0,0,0.06);
  backdrop-filter: blur(8px) saturate(1.5);
  -webkit-backdrop-filter: blur(8px) saturate(1.5);
  transition: background 0.3s, box-shadow 0.3s, border 0.3s;
}

/* ========== LIQUID GLASS EFFECT (Based on liquidglass.md) ========== */

/* SVG Filter for glass distortion */
.glass-distortion-filter {
  position: absolute;
  width: 0;
  height: 0;
  pointer-events: none;
}

/* Base liquid glass input */
.liquid-glass-input {
  position: relative;
  overflow: hidden;
  border-radius: 0.875rem;
  height: 2.75rem;
  border: none;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 
    0 8px 32px rgba(31, 38, 135, 0.37),
    0 4px 16px rgba(31, 38, 135, 0.09);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 2.2);
  backdrop-filter: blur(40px) saturate(180%);
  -webkit-backdrop-filter: blur(40px) saturate(180%);
}

/* Wavy border gradient effect */
.liquid-glass-input::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(45deg, 
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.1) 15%,
    rgba(255, 255, 255, 0.4) 30%,
    rgba(255, 255, 255, 0.05) 45%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0.08) 75%,
    rgba(255, 255, 255, 0.6) 100%);
  background-size: 300% 300%;
  animation: liquidBorder 6s ease-in-out infinite;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: subtract;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: subtract;
}

@keyframes liquidBorder {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 30%; }
  50% { background-position: 100% 70%; }
  75% { background-position: 0% 70%; }
}

/* Inner glass layer */
.liquid-glass-input::after {
  content: '';
  position: absolute;
  inset: 2px;
  z-index: 1;
  background: rgba(255, 255, 255, 0.05);
  border-radius: calc(0.875rem - 2px);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Specular highlight layer */
.liquid-glass-input .glass-specular {
  position: absolute;
  inset: 0;
  z-index: 2;
  border-radius: inherit;
  overflow: hidden;
  box-shadow: 
    inset 1px 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 0 8px rgba(255, 255, 255, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  pointer-events: none;
}

/* Input content layer */
.liquid-glass-input input {
  position: relative;
  z-index: 3;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  height: 100%;
  padding: 0 1rem;
  font-size: 0.875rem;
  color: #1e293b;
  font-weight: 500;
}

.liquid-glass-input input::placeholder {
  color: rgba(30, 41, 59, 0.6);
}

/* Green variant */
.liquid-glass-green::after {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
}

.liquid-glass-green {
  border-color: rgba(34, 197, 94, 0.4);
  box-shadow: 
    0 8px 32px rgba(34, 197, 94, 0.25),
    0 4px 16px rgba(34, 197, 94, 0.15),
    inset 0 0 0 1px rgba(34, 197, 94, 0.2);
}

/* Red variant */
.liquid-glass-red::after {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
}

.liquid-glass-red {
  border-color: rgba(239, 68, 68, 0.4);
  box-shadow: 
    0 8px 32px rgba(239, 68, 68, 0.25),
    0 4px 16px rgba(239, 68, 68, 0.15),
    inset 0 0 0 1px rgba(239, 68, 68, 0.2);
}

/* ========== END LIQUID GLASS EFFECT ========== */

/* ========== LIQUID GLASS CARD (for dev-gift) ========== */
.liquid-glass-card {
  position: relative;
  overflow: hidden;
  border-radius: 1.25rem;
  background: rgba(255, 255, 255, 0.13);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.18), 0 4px 16px rgba(31, 38, 135, 0.09);
  border: none;
  min-width: 340px;
  min-height: 320px;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(32px) saturate(180%);
  -webkit-backdrop-filter: blur(32px) saturate(180%);
  transition: box-shadow 0.4s cubic-bezier(0.175, 0.885, 0.32, 2.2);
}
.liquid-glass-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: linear-gradient(120deg, rgba(120,180,255,0.18) 0%, rgba(255,255,255,0.22) 100%);
  z-index: 0;
  pointer-events: none;
}
.liquid-glass-card .glass-specular {
  position: absolute;
  inset: 0;
  z-index: 1;
  border-radius: inherit;
  box-shadow: inset 1px 1px 0 rgba(255,255,255,0.7), inset 0 0 8px rgba(255,255,255,0.5);
  pointer-events: none;
}
.liquid-glass-card > * {
  position: relative;
  z-index: 2;
}
/* ========== END LIQUID GLASS CARD ========== */

/* ========== PDF VIEWER GLASSMORPHIC STYLING ========== */

/* Style the PDF viewer toolbar */
.rpv-default-layout__toolbar {
  background: rgba(255, 255, 255, 0.85) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Style PDF viewer toolbar buttons */
.rpv-default-layout__toolbar button {
  background: rgba(255, 255, 255, 0.7) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
  color: #374151 !important;
}

.rpv-default-layout__toolbar button:hover {
  background: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}

.rpv-default-layout__toolbar button:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1) !important;
}

/* Style the zoom input */
.rpv-zoom__input {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  border-radius: 6px !important;
}

/* PDF viewer main content area */
.rpv-default-layout__main {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
}

/* Style search input if used */
.rpv-search__input {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  border-radius: 8px !important;
}

/* Style any dropdowns */
.rpv-core__popover-body {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(12px) !important;
  border: 1px solid rgba(59, 130, 246, 0.1) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

/* ========== END PDF VIEWER STYLING ========== */
# Environment Variables for Vercel Deployment
# Copy this file to .env.local for local development
# Set these variables in your Vercel dashboard for production

# Backend API URL (Required)
# For local development: http://localhost:5000
# For production: https://your-backend-api.vercel.app
BACKEND_URL=https://your-backend-api.vercel.app
NEXT_PUBLIC_BACKEND_URL=https://your-backend-api.vercel.app

# Development mode flag
# Set to "false" for production deployment
NEXT_PUBLIC_LOCAL_DEV=false

# OpenRouter API Key for LLM chat functionality
# Get your API key from https://openrouter.ai/
OPENROUTER_API_KEY=your_openrouter_api_key_here

# GitHub Token for file storage
# Create a personal access token with repo permissions
# https://github.com/settings/tokens
GITHUB_TOKEN=your_github_token_here

# Optional: Additional environment variables
# Add any other environment variables your backend might need

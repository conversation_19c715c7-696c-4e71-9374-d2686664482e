{"name": "mimir-chat", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "./deploy.sh", "deploy:vercel": "vercel --prod", "preview": "vercel", "env:setup": "cp .env.example .env.local", "type-check": "tsc --noEmit"}, "dependencies": {"@octokit/rest": "^22.0.0", "@radix-ui/react-slot": "^1.2.3", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/get-file": "^3.12.0", "@react-pdf-viewer/print": "^3.12.0", "@react-pdf-viewer/toolbar": "^3.12.0", "@react-pdf-viewer/zoom": "^3.12.0", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "formidable": "^3.5.4", "lucide-react": "^0.511.0", "next": "15.3.3", "pdf-parse": "^1.1.1", "pdfjs-dist": "^3.4.120", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/formidable": "^3.4.5", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "ignore-loader": "^0.1.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}